#Requires -RunAsAdministrator

param(
    [string]$CPUName = "",
    [string]$CPUSpeed = "",
    [switch]$Analyze,
    [switch]$Restore,
    [switch]$List
)

Write-Host "================================================================" -ForegroundColor Red
Write-Host "    COMPLETE CPU SPOOFER - DEVICE MANAGER & SYSTEM PROPERTIES" -ForegroundColor Red
Write-Host "                   MALWARE ANALYSIS TOOL" -ForegroundColor Red
Write-Host "================================================================" -ForegroundColor Red
Write-Host ""

# Admin check
if (!([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
    Write-Host "Administrator privileges required!" -ForegroundColor Red
    exit 1
}

# Predefined CPU profiles
$CPUProfiles = @{
    1 = @{ Name = "Intel Core i9-13900K"; Speed = 3000; Vendor = "GenuineIntel"; ID = "Intel64 Family 6 Model 183 Stepping 0" }
    2 = @{ Name = "AMD Ryzen 9 7950X"; Speed = 4500; Vendor = "AuthenticAMD"; ID = "AMD64 Family 25 Model 97 Stepping 2" }
    3 = @{ Name = "Intel Core i7-12700K"; Speed = 3600; Vendor = "GenuineIntel"; ID = "Intel64 Family 6 Model 151 Stepping 2" }
    4 = @{ Name = "AMD Ryzen 7 5800X"; Speed = 3800; Vendor = "AuthenticAMD"; ID = "AMD64 Family 25 Model 33 Stepping 2" }
    5 = @{ Name = "Intel Core i5-11600K"; Speed = 3900; Vendor = "GenuineIntel"; ID = "Intel64 Family 6 Model 167 Stepping 1" }
    6 = @{ Name = "AMD Ryzen 5 5600X"; Speed = 3700; Vendor = "AuthenticAMD"; ID = "AMD64 Family 25 Model 33 Stepping 0" }
    7 = @{ Name = "Intel Xeon E5-2690 v4"; Speed = 2600; Vendor = "GenuineIntel"; ID = "Intel64 Family 6 Model 79 Stepping 1" }
    8 = @{ Name = "AMD EPYC 7763"; Speed = 2450; Vendor = "AuthenticAMD"; ID = "AMD64 Family 25 Model 1 Stepping 1" }
    9 = @{ Name = "Intel Core i3-10100"; Speed = 3600; Vendor = "GenuineIntel"; ID = "Intel64 Family 6 Model 165 Stepping 3" }
    10 = @{ Name = "AMD Ryzen 3 3300X"; Speed = 3800; Vendor = "AuthenticAMD"; ID = "AMD64 Family 23 Model 113 Stepping 0" }
}

# List profiles
if ($List) {
    Write-Host "Available CPU Profiles:" -ForegroundColor Cyan
    Write-Host "======================" -ForegroundColor Cyan
    foreach ($key in 1..10) {
        $cpu = $CPUProfiles[$key]
        Write-Host "$key. $($cpu.Name)" -ForegroundColor White
        Write-Host "   $($cpu.Vendor) - $($cpu.Speed) MHz" -ForegroundColor Gray
    }
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\CompleteCPUSpoofer.ps1 -CPUName ""AMD Ryzen 9 7950X"" -CPUSpeed ""4500""" -ForegroundColor White
    Write-Host "  .\CompleteCPUSpoofer.ps1 -CPUName ""[Profile:2]""  # Use profile 2" -ForegroundColor White
    exit 0
}

# Analyze current state
function Analyze-CurrentState {
    Write-Host "Current CPU Information:" -ForegroundColor Cyan
    Write-Host "========================" -ForegroundColor Cyan
    
    # Device Manager info
    Write-Host ""
    Write-Host "Device Manager Sources:" -ForegroundColor Yellow
    
    # Check hardware registry
    $hwPath = "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\0"
    if (Test-Path $hwPath) {
        $cpu = Get-ItemProperty $hwPath
        Write-Host "  Registry: $($cpu.ProcessorNameString)" -ForegroundColor White
    }
    
    # Check Device Manager classes
    $processorClass = Get-ChildItem "HKLM:\SYSTEM\CurrentControlSet\Control\Class" -ErrorAction SilentlyContinue | 
        Where-Object { 
            $class = Get-ItemProperty $_.PSPath -ErrorAction SilentlyContinue
            $class.Class -eq "Processor"
        }
    
    if ($processorClass) {
        $devices = Get-ChildItem $processorClass.PSPath -ErrorAction SilentlyContinue
        foreach ($device in $devices) {
            $props = Get-ItemProperty $device.PSPath -ErrorAction SilentlyContinue
            if ($props.FriendlyName) {
                Write-Host "  Device Class: $($props.FriendlyName)" -ForegroundColor White
            }
        }
    }
    
    # System Properties info
    Write-Host ""
    Write-Host "System Properties Sources:" -ForegroundColor Yellow
    
    # Environment variables
    Write-Host "  Environment: $env:PROCESSOR_IDENTIFIER" -ForegroundColor White
    
    # WMI
    try {
        $wmi = Get-WmiObject Win32_Processor | Select-Object -First 1
        Write-Host "  WMI: $($wmi.Name)" -ForegroundColor White
        Write-Host "  Speed: $($wmi.MaxClockSpeed) MHz" -ForegroundColor White
    } catch {
        Write-Host "  WMI: Cannot access" -ForegroundColor Red
    }
}

# Create comprehensive backup
function Create-CompleteBackup {
    $timestamp = Get-Date -Format 'yyyyMMdd_HHmmss'
    $backupDir = "$env:TEMP\CompleteCPUBackup_$timestamp"
    New-Item -Path $backupDir -ItemType Directory -Force | Out-Null
    
    Write-Host "Creating comprehensive backup..." -ForegroundColor Yellow
    
    # Backup all CPU-related registry sections
    $sections = @(
        @{Path="HKEY_LOCAL_MACHINE\HARDWARE\DESCRIPTION\System\CentralProcessor"; Name="Hardware"},
        @{Path="HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Class"; Name="DeviceClass"},
        @{Path="HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum"; Name="DeviceEnum"},
        @{Path="HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SystemInformation"; Name="SystemInfo"},
        @{Path="HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; Name="Environment"}
    )
    
    foreach ($section in $sections) {
        $backupFile = "$backupDir\$($section.Name).reg"
        reg export $section.Path $backupFile /y 2>$null
    }
    
    Write-Host "Backup created: $backupDir" -ForegroundColor Green
    return $backupDir
}

# Main modification function
function Apply-CompleteCPUSpoof {
    param(
        [string]$NewCPUName,
        [string]$NewCPUSpeed,
        [string]$Vendor,
        [string]$Identifier
    )
    
    $totalModified = 0
    
    # Phase 1: Hardware Registry (affects both Device Manager and System Properties)
    Write-Host ""
    Write-Host "Phase 1: Hardware Registry Modification..." -ForegroundColor Cyan
    
    for ($i = 0; $i -lt 32; $i++) {
        $path = "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\$i"
        if (Test-Path $path) {
            try {
                Set-ItemProperty -Path $path -Name "ProcessorNameString" -Value $NewCPUName -Force
                Set-ItemProperty -Path $path -Name "Identifier" -Value $Identifier -Force
                Set-ItemProperty -Path $path -Name "VendorIdentifier" -Value $Vendor -Force
                Set-ItemProperty -Path $path -Name "~MHz" -Value ([int]$NewCPUSpeed) -Type DWord -Force
                Write-Host "  Core $i - Modified" -ForegroundColor Green
                $totalModified++
            } catch {
                Write-Host "  Core $i - Failed" -ForegroundColor Red
            }
        }
    }
    
    # Phase 2: Device Manager specific modifications
    Write-Host ""
    Write-Host "Phase 2: Device Manager Modifications..." -ForegroundColor Cyan
    
    # Modify Device Class entries
    $processorClass = Get-ChildItem "HKLM:\SYSTEM\CurrentControlSet\Control\Class" -ErrorAction SilentlyContinue | 
        Where-Object { 
            $class = Get-ItemProperty $_.PSPath -ErrorAction SilentlyContinue
            $class.Class -eq "Processor"
        }
    
    if ($processorClass) {
        $devices = Get-ChildItem $processorClass.PSPath -ErrorAction SilentlyContinue
        foreach ($device in $devices) {
            try {
                Set-ItemProperty -Path $device.PSPath -Name "FriendlyName" -Value $NewCPUName -Force -ErrorAction SilentlyContinue
                Set-ItemProperty -Path $device.PSPath -Name "DeviceDesc" -Value $NewCPUName -Force -ErrorAction SilentlyContinue
                Write-Host "  Device class updated" -ForegroundColor Green
                $totalModified++
            } catch {
                # Skip protected entries
            }
        }
    }
    
    # Modify ACPI Enum entries
    $enumPaths = @(
        "HKLM:\SYSTEM\CurrentControlSet\Enum\ACPI",
        "HKLM:\SYSTEM\CurrentControlSet\Enum\Root\ACPI"
    )
    
    foreach ($enumPath in $enumPaths) {
        if (Test-Path $enumPath) {
            $cpuDevices = Get-ChildItem $enumPath -ErrorAction SilentlyContinue | 
                Where-Object { $_.PSChildName -match "Processor|CPU|GenuineIntel|AuthenticAMD" }
            
            foreach ($device in $cpuDevices) {
                $instances = Get-ChildItem $device.PSPath -ErrorAction SilentlyContinue
                foreach ($instance in $instances) {
                    try {
                        Set-ItemProperty -Path $instance.PSPath -Name "FriendlyName" -Value $NewCPUName -Force -ErrorAction SilentlyContinue
                        Set-ItemProperty -Path $instance.PSPath -Name "DeviceDesc" -Value $NewCPUName -Force -ErrorAction SilentlyContinue
                        $totalModified++
                    } catch {
                        # Skip protected entries
                    }
                }
            }
        }
    }
    
    # Phase 3: System Properties specific modifications
    Write-Host ""
    Write-Host "Phase 3: System Properties Modifications..." -ForegroundColor Cyan
    
    # Environment variables
    try {
        $envPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Environment"
        Set-ItemProperty -Path $envPath -Name "PROCESSOR_IDENTIFIER" -Value $Identifier -Force
        [Environment]::SetEnvironmentVariable("PROCESSOR_IDENTIFIER", $Identifier, "Machine")
        Write-Host "  Environment variables updated" -ForegroundColor Green
        $totalModified++
    } catch {
        Write-Host "  Environment variables failed" -ForegroundColor Red
    }
    
    # System Information registry
    try {
        $sysInfoPath = "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation"
        if (!(Test-Path $sysInfoPath)) {
            New-Item -Path $sysInfoPath -Force | Out-Null
        }
        
        New-ItemProperty -Path $sysInfoPath -Name "ProcessorNameString" -Value $NewCPUName -PropertyType String -Force | Out-Null
        New-ItemProperty -Path $sysInfoPath -Name "ProcessorSpeed" -Value ([int]$NewCPUSpeed) -PropertyType DWord -Force | Out-Null
        Write-Host "  System information updated" -ForegroundColor Green
        $totalModified++
    } catch {
        Write-Host "  System information failed" -ForegroundColor Red
    }
    
    # Phase 4: WMI modification attempt
    Write-Host ""
    Write-Host "Phase 4: WMI Modifications..." -ForegroundColor Cyan
    try {
        # Stop WMI
        Stop-Service "WinMgmt" -Force -ErrorAction SilentlyContinue
        Start-Sleep -Seconds 2
        
        # Create MOF for WMI modification
        $mofContent = @"
#pragma namespace("\\\\.\\root\\cimv2")
instance of __Win32Provider as `$P
{
    Name = "CIMWin32";
};
instance of __InstanceModificationEvent
{
    TargetInstance = instance of Win32_Processor
    {
        Name = "$NewCPUName";
        Description = "$NewCPUName";
        MaxClockSpeed = $([int]$NewCPUSpeed);
    };
};
"@
        
        $mofFile = "$env:TEMP\CompleteCPU.mof"
        $mofContent | Out-File $mofFile -Encoding ASCII
        mofcomp $mofFile 2>$null
        Remove-Item $mofFile -Force
        
        # Restart WMI
        Start-Service "WinMgmt" -ErrorAction SilentlyContinue
        Write-Host "  WMI updated" -ForegroundColor Green
        $totalModified++
    } catch {
        Write-Host "  WMI update failed (this is normal)" -ForegroundColor Yellow
    }
    
    # Phase 5: System refresh
    Write-Host ""
    Write-Host "Phase 5: System Refresh..." -ForegroundColor Cyan
    try {
        # Close Device Manager if open
        Get-Process "mmc" -ErrorAction SilentlyContinue | Where-Object {$_.MainWindowTitle -like "*Device Manager*"} | Stop-Process -Force
        
        # Restart services
        Restart-Service "PlugPlay" -Force -ErrorAction SilentlyContinue
        Restart-Service "SystemEventsBroker" -Force -ErrorAction SilentlyContinue
        
        # Force hardware rescan
        Start-Process "pnputil.exe" -ArgumentList "/scan-devices" -Wait -NoNewWindow
        
        # Restart explorer for System Properties refresh
        Stop-Process -Name "explorer" -Force -ErrorAction SilentlyContinue
        Start-Sleep -Seconds 2
        Start-Process "explorer.exe"
        
        Write-Host "  System refreshed" -ForegroundColor Green
    } catch {
        Write-Host "  Some refresh operations failed" -ForegroundColor Yellow
    }
    
    return $totalModified
}

# Main execution
if ($Analyze) {
    Analyze-CurrentState
    exit 0
}

if ($Restore) {
    Write-Host "Looking for backups..." -ForegroundColor Yellow
    $backups = Get-ChildItem "$env:TEMP\CompleteCPUBackup_*" | Sort-Object LastWriteTime -Descending
    
    if ($backups.Count -eq 0) {
        Write-Host "No backups found!" -ForegroundColor Red
        exit 1
    }
    
    $latest = $backups[0]
    Write-Host "Restoring from: $latest" -ForegroundColor Cyan
    
    # Restore all reg files in backup directory
    $regFiles = Get-ChildItem "$($latest.FullName)\*.reg"
    foreach ($regFile in $regFiles) {
        Write-Host "  Restoring $($regFile.Name)..." -ForegroundColor Yellow
        reg import $regFile.FullName 2>$null
    }
    
    Write-Host "Restoration complete! Restart required." -ForegroundColor Green
    exit 0
}

# Process CPU selection
if ($CPUName -eq "") {
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  List profiles:    .\CompleteCPUSpoofer.ps1 -List" -ForegroundColor Cyan
    Write-Host "  Analyze current:  .\CompleteCPUSpoofer.ps1 -Analyze" -ForegroundColor Cyan
    Write-Host "  Custom CPU:       .\CompleteCPUSpoofer.ps1 -CPUName ""AMD Ryzen 9 7950X"" -CPUSpeed ""4500""" -ForegroundColor Cyan
    Write-Host "  Use profile:      .\CompleteCPUSpoofer.ps1 -CPUName ""[Profile:2]""" -ForegroundColor Cyan
    Write-Host "  Restore:          .\CompleteCPUSpoofer.ps1 -Restore" -ForegroundColor Cyan
    exit 1
}

# Check if using profile
$selectedCPU = $null
if ($CPUName -match "\[Profile:(\d+)\]") {
    $profileNum = [int]$matches[1]
    if ($CPUProfiles.ContainsKey($profileNum)) {
        $selectedCPU = $CPUProfiles[$profileNum]
        $CPUName = $selectedCPU.Name
        $CPUSpeed = $selectedCPU.Speed.ToString()
        $vendor = $selectedCPU.Vendor
        $identifier = $selectedCPU.ID
    } else {
        Write-Host "Invalid profile number: $profileNum" -ForegroundColor Red
        exit 1
    }
} else {
    # Custom CPU
    if ($CPUSpeed -eq "") {
        $CPUSpeed = "3000"
    }
    
    # Auto-detect vendor and identifier
    if ($CPUName -match "AMD") {
        $vendor = "AuthenticAMD"
        $identifier = "AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD"
    } else {
        $vendor = "GenuineIntel"
        $identifier = "Intel64 Family 6 Model 183 Stepping 0, GenuineIntel"
    }
}

Write-Host "This will modify BOTH Device Manager and System Properties to show:" -ForegroundColor Yellow
Write-Host "  CPU Name: $CPUName" -ForegroundColor Cyan
Write-Host "  CPU Speed: $CPUSpeed MHz" -ForegroundColor Cyan
Write-Host "  Vendor: $vendor" -ForegroundColor Cyan
Write-Host ""
Write-Host "This affects:" -ForegroundColor Yellow
Write-Host "  ✓ Device Manager > Processors" -ForegroundColor White
Write-Host "  ✓ System Properties (This PC > Properties)" -ForegroundColor White
Write-Host "  ✓ Environment variables" -ForegroundColor White
Write-Host "  ✓ WMI queries" -ForegroundColor White
Write-Host ""
Write-Host "Continue? (y/N): " -NoNewline -ForegroundColor Yellow
if ((Read-Host) -ne 'y') {
    Write-Host "Cancelled." -ForegroundColor Yellow
    exit 0
}

# Create backup
$backupPath = Create-CompleteBackup

# Apply modifications
$result = Apply-CompleteCPUSpoof -NewCPUName $CPUName -NewCPUSpeed $CPUSpeed -Vendor $vendor -Identifier $identifier

if ($result -gt 0) {
    Write-Host ""
    Write-Host "================================================================" -ForegroundColor Green
    Write-Host "           COMPLETE CPU SPOOFING APPLIED SUCCESSFULLY!" -ForegroundColor Green
    Write-Host "================================================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Modified $result registry entries" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "To see changes:" -ForegroundColor Yellow
    Write-Host "1. Device Manager: Run 'devmgmt.msc' (may need restart)" -ForegroundColor White
    Write-Host "2. System Properties: Run 'control system'" -ForegroundColor White
    Write-Host "3. Or right-click This PC > Properties" -ForegroundColor White
    Write-Host ""
    Write-Host "Some changes require a restart to take full effect." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Backup location: $backupPath" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "1. Open Device Manager" -ForegroundColor White
    Write-Host "2. Open System Properties" -ForegroundColor White
    Write-Host "3. Restart now" -ForegroundColor White
    Write-Host "4. Exit" -ForegroundColor White
    Write-Host ""
    Write-Host "Choose (1-4): " -NoNewline -ForegroundColor Cyan
    $choice = Read-Host
    
    switch ($choice) {
        "1" { Start-Process "devmgmt.msc" }
        "2" { Start-Process "control" -ArgumentList "system" }
        "3" { 
            Write-Host "Restarting in 10 seconds..." -ForegroundColor Red
            Start-Sleep -Seconds 10
            Restart-Computer -Force 
        }
        default { Write-Host "Done!" -ForegroundColor Green }
    }
} else {
    Write-Host ""
    Write-Host "No modifications were successful!" -ForegroundColor Red
    Write-Host "This system may have additional protections." -ForegroundColor Yellow
}
