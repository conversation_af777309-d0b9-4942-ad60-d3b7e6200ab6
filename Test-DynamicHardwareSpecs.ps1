#Requires -RunAsAdministrator

<#
.SYNOPSIS
    Test script to verify dynamic hardware specifications spoofing
.DESCRIP<PERSON>ON
    Tests the updated hardware spoofing implementation to ensure dynamic specifications work correctly
#>

param(
    [switch]$Analyze,
    [switch]$TestDynamicSpecs,
    [switch]$ShowSystemInfo
)

Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "    DYNAMIC HARDWARE SPECIFICATIONS TEST SCRIPT" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""

# Admin check
if (!([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
    Write-Host "Administrator privileges required!" -ForegroundColor Red
    exit 1
}

function Get-SystemInformation {
    Write-Host "Current System Information:" -ForegroundColor Yellow
    Write-Host "===========================" -ForegroundColor Yellow
    
    # CPU Information
    Write-Host "`nCPU Information:" -ForegroundColor Cyan
    try {
        $cpu = Get-WmiObject Win32_Processor | Select-Object -First 1
        Write-Host "  Name: $($cpu.Name)" -ForegroundColor White
        Write-Host "  Cores: $($cpu.NumberOfCores)" -ForegroundColor White
        Write-Host "  Logical Processors: $($cpu.NumberOfLogicalProcessors)" -ForegroundColor White
        Write-Host "  Max Clock Speed: $($cpu.MaxClockSpeed) MHz" -ForegroundColor White
        Write-Host "  Manufacturer: $($cpu.Manufacturer)" -ForegroundColor White
    } catch {
        Write-Host "  CPU WMI: Cannot access" -ForegroundColor Red
    }
    
    # Environment Variables
    Write-Host "`nEnvironment Variables:" -ForegroundColor Cyan
    Write-Host "  NUMBER_OF_PROCESSORS: $env:NUMBER_OF_PROCESSORS" -ForegroundColor White
    Write-Host "  PROCESSOR_IDENTIFIER: $env:PROCESSOR_IDENTIFIER" -ForegroundColor White
    
    # Memory Information
    Write-Host "`nMemory Information:" -ForegroundColor Cyan
    try {
        $memory = Get-WmiObject Win32_ComputerSystem
        $totalRAM = [math]::Round($memory.TotalPhysicalMemory / 1GB, 2)
        Write-Host "  Total Physical Memory: $totalRAM GB" -ForegroundColor White
        
        $memoryModules = Get-WmiObject Win32_PhysicalMemory
        $moduleCount = $memoryModules.Count
        Write-Host "  Memory Modules: $moduleCount" -ForegroundColor White
    } catch {
        Write-Host "  Memory WMI: Cannot access" -ForegroundColor Red
    }
    
    # GPU Information
    Write-Host "`nGPU Information:" -ForegroundColor Cyan
    try {
        $gpu = Get-WmiObject Win32_VideoController | Where-Object { $_.Name -notlike "*Basic*" } | Select-Object -First 1
        if ($gpu) {
            Write-Host "  Name: $($gpu.Name)" -ForegroundColor White
            $vramMB = [math]::Round($gpu.AdapterRAM / 1MB)
            Write-Host "  VRAM: $vramMB MB" -ForegroundColor White
            Write-Host "  Driver Version: $($gpu.DriverVersion)" -ForegroundColor White
        }
    } catch {
        Write-Host "  GPU WMI: Cannot access" -ForegroundColor Red
    }
    
    # Storage Information
    Write-Host "`nStorage Information:" -ForegroundColor Cyan
    try {
        $disks = Get-WmiObject Win32_DiskDrive
        foreach ($disk in $disks) {
            $sizeGB = [math]::Round($disk.Size / 1GB)
            Write-Host "  $($disk.Model): $sizeGB GB" -ForegroundColor White
        }
    } catch {
        Write-Host "  Storage WMI: Cannot access" -ForegroundColor Red
    }
    
    # Registry Information
    Write-Host "`nRegistry Information:" -ForegroundColor Cyan
    
    # CPU Registry
    $cpuPath = "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\0"
    if (Test-Path $cpuPath) {
        $cpuReg = Get-ItemProperty $cpuPath
        Write-Host "  CPU Registry Name: $($cpuReg.ProcessorNameString)" -ForegroundColor White
        Write-Host "  CPU Registry MHz: $($cpuReg.'~MHz')" -ForegroundColor White
    }
    
    # Count CPU cores in registry
    $coreCount = 0
    for ($i = 0; $i -lt 64; $i++) {
        if (Test-Path "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\$i") {
            $coreCount++
        } else {
            break
        }
    }
    Write-Host "  Registry CPU Cores: $coreCount" -ForegroundColor White
    
    # System Information Registry
    $sysInfoPath = "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation"
    if (Test-Path $sysInfoPath) {
        $sysInfo = Get-ItemProperty $sysInfoPath -ErrorAction SilentlyContinue
        if ($sysInfo.TotalPhysicalMemory) {
            $regRAM = [math]::Round($sysInfo.TotalPhysicalMemory / 1GB, 2)
            Write-Host "  Registry RAM: $regRAM GB" -ForegroundColor White
        }
        if ($sysInfo.NumberOfProcessors) {
            Write-Host "  Registry Processor Count: $($sysInfo.NumberOfProcessors)" -ForegroundColor White
        }
    }
}

function Test-DynamicHardwareSpecifications {
    Write-Host "Testing Dynamic Hardware Specifications..." -ForegroundColor Yellow
    Write-Host "==========================================" -ForegroundColor Yellow
    
    try {
        # Import required modules
        Write-Host "`nImporting modules..." -ForegroundColor Cyan
        Import-Module ".\Modules\Core\Logging\Logging.psm1" -Force
        Import-Module ".\Modules\Core\Utilities\Utilities.psm1" -Force
        Import-Module ".\Modules\Registry\RegistryPrivileges\RegistryPrivileges.psm1" -Force
        Import-Module ".\Modules\Hardware\HardwareProfiles\HardwareProfiles.psm1" -Force
        Import-Module ".\Modules\Hardware\SystemInformation\SystemInformationSpoofing.psm1" -Force
        Import-Module ".\Modules\Hardware\AdvancedSpoofing\AdvancedSpoofing.psm1" -Force
        
        # Load configuration
        Write-Host "Loading configuration..." -ForegroundColor Cyan
        $config = Import-PowerShellDataFile ".\config.psd1"
        
        # Generate hardware profile
        Write-Host "Generating random hardware profile..." -ForegroundColor Cyan
        $hardwareProfile = Get-RealisticHardwareProfile
        
        Write-Host "`nSelected Hardware Profile:" -ForegroundColor Green
        Write-Host "  CPU: $($hardwareProfile.CPU.Name)" -ForegroundColor White
        Write-Host "  CPU Cores: $($hardwareProfile.CPU.Cores)" -ForegroundColor White
        Write-Host "  CPU Threads: $($hardwareProfile.CPU.Threads)" -ForegroundColor White
        Write-Host "  CPU Base Freq: $($hardwareProfile.CPU.BaseFreq) MHz" -ForegroundColor White
        Write-Host "  GPU: $($hardwareProfile.GPU.Model)" -ForegroundColor White
        Write-Host "  GPU VRAM: $([math]::Round($hardwareProfile.GPU.VRAM / 1MB)) MB" -ForegroundColor White
        Write-Host "  Storage: $($hardwareProfile.Storage.Model)" -ForegroundColor White
        Write-Host "  Storage Size: $([math]::Round($hardwareProfile.Storage.Capacity / 1GB)) GB" -ForegroundColor White
        Write-Host "  Memory: $($hardwareProfile.Memory.Model)" -ForegroundColor White
        Write-Host "  Memory Size: $([math]::Round($hardwareProfile.Memory.Capacity / 1GB)) GB" -ForegroundColor White
        
        Write-Host "`nApplying comprehensive hardware spoofing..." -ForegroundColor Cyan
        
        # Apply CPU spoofing
        Write-Host "  Applying CPU spoofing..." -ForegroundColor Yellow
        $cpuResult = Invoke-AdvancedCPUSpoofing -Config $config -HardwareProfile $hardwareProfile
        
        # Apply System Information spoofing
        Write-Host "  Applying System Information spoofing..." -ForegroundColor Yellow
        $sysInfoResult = Invoke-SystemInformationSpoofing -Config $config -HardwareProfile $hardwareProfile
        
        # Display results
        Write-Host "`nSpoofing Results:" -ForegroundColor Green
        if ($cpuResult.Success) {
            Write-Host "  CPU Spoofing: SUCCESS - $($cpuResult.Message)" -ForegroundColor Green
        } else {
            Write-Host "  CPU Spoofing: FAILED - $($cpuResult.Message)" -ForegroundColor Red
        }
        
        if ($sysInfoResult.Success) {
            Write-Host "  System Info Spoofing: SUCCESS - $($sysInfoResult.Message)" -ForegroundColor Green
            Write-Host "  Total Modifications: $($sysInfoResult.TotalModifications)" -ForegroundColor Green
        } else {
            Write-Host "  System Info Spoofing: FAILED - $($sysInfoResult.Message)" -ForegroundColor Red
        }
        
        return ($cpuResult.Success -and $sysInfoResult.Success)
    }
    catch {
        Write-Host "`nDynamic Hardware Specifications Test Failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Show-SystemPropertiesInfo {
    Write-Host "System Properties Information:" -ForegroundColor Yellow
    Write-Host "==============================" -ForegroundColor Yellow
    
    Write-Host "`nTo verify the changes:" -ForegroundColor Cyan
    Write-Host "1. Open System Properties:" -ForegroundColor White
    Write-Host "   - Right-click 'This PC' → Properties" -ForegroundColor Gray
    Write-Host "   - Or run: control system" -ForegroundColor Gray
    Write-Host ""
    Write-Host "2. Check Device Manager:" -ForegroundColor White
    Write-Host "   - Run: devmgmt.msc" -ForegroundColor Gray
    Write-Host "   - Expand 'Processors' section" -ForegroundColor Gray
    Write-Host ""
    Write-Host "3. Check Task Manager:" -ForegroundColor White
    Write-Host "   - Ctrl+Shift+Esc → Performance tab" -ForegroundColor Gray
    Write-Host "   - Check CPU, Memory, and GPU sections" -ForegroundColor Gray
    Write-Host ""
    Write-Host "4. Check System Information:" -ForegroundColor White
    Write-Host "   - Run: msinfo32" -ForegroundColor Gray
    Write-Host "   - Check System Summary section" -ForegroundColor Gray
}

# Main execution
if ($Analyze) {
    Get-SystemInformation
    exit 0
}

if ($ShowSystemInfo) {
    Show-SystemPropertiesInfo
    exit 0
}

if ($TestDynamicSpecs) {
    Write-Host "BEFORE DYNAMIC HARDWARE SPOOFING:" -ForegroundColor Magenta
    Write-Host "==================================" -ForegroundColor Magenta
    Get-SystemInformation
    
    Write-Host "`n`n"
    $success = Test-DynamicHardwareSpecifications
    
    Write-Host "`n`n"
    Write-Host "AFTER DYNAMIC HARDWARE SPOOFING:" -ForegroundColor Magenta
    Write-Host "=================================" -ForegroundColor Magenta
    Get-SystemInformation
    
    if ($success) {
        Write-Host "`n`nDYNAMIC HARDWARE SPECIFICATIONS TEST: PASSED" -ForegroundColor Green
        Write-Host "Hardware specifications should now reflect the spoofed profile" -ForegroundColor Green
        Write-Host "Check System Properties, Device Manager, and Task Manager" -ForegroundColor Yellow
    } else {
        Write-Host "`n`nDYNAMIC HARDWARE SPECIFICATIONS TEST: FAILED" -ForegroundColor Red
    }
    
    Write-Host "`n"
    Show-SystemPropertiesInfo
    exit 0
}

# Default usage
Write-Host "Usage:" -ForegroundColor Yellow
Write-Host "  Analyze current specs:     .\Test-DynamicHardwareSpecs.ps1 -Analyze" -ForegroundColor Cyan
Write-Host "  Test dynamic spoofing:     .\Test-DynamicHardwareSpecs.ps1 -TestDynamicSpecs" -ForegroundColor Cyan
Write-Host "  Show verification info:    .\Test-DynamicHardwareSpecs.ps1 -ShowSystemInfo" -ForegroundColor Cyan
