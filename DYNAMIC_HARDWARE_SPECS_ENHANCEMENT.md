# Dynamic Hardware Specifications Enhancement

## Overview

This enhancement addresses the issue where hardware spoofing was displaying fixed host machine values instead of dynamically reflecting the spoofed hardware profiles. The system now properly displays hardware specifications that match the selected spoofing profiles.

## Problem Addressed

**Primary Issue**: The CPU core count was showing a fixed value of 4 cores from the host machine instead of dynamically reflecting the spoofed processor's actual core count.

**Additional Issues**:
- Graphics card memory showing host GPU values instead of spoofed GPU specifications
- Storage capacity displaying actual host storage instead of spoofed storage profiles
- RAM values were fixed instead of being randomized within realistic ranges

## Solution Implemented

### New Module: SystemInformationSpoofing.psm1

Created a comprehensive system information spoofing module that dynamically updates hardware specifications based on spoofed profiles.

**Location**: `Modules\Hardware\SystemInformation\SystemInformationSpoofing.psm1`

#### Key Functions:

1. **`Invoke-CPUCoreCountSpoofing`**
   - Updates CPU core count to match spoofed processor specifications
   - Modifies registry keys that control CPU core count display
   - Updates environment variables for immediate effect
   - Creates additional CPU core registry entries as needed

2. **`Invoke-RAMSpoofing`**
   - Generates random but realistic RAM sizes (4GB, 8GB, 16GB, 32GB)
   - Updates multiple registry locations for RAM size display
   - Modifies WMI-related registry entries

3. **`Invoke-GPUMemorySpoofing`**
   - Updates GPU memory size to match spoofed graphics card
   - Modifies GPU memory registry locations
   - Updates system information registry for GPU specifications

4. **`Invoke-StorageCapacitySpoofing`**
   - Updates storage device sizes to reflect spoofed storage devices
   - Modifies storage capacity registry locations
   - Updates system information for storage specifications

5. **`Invoke-SystemInformationSpoofing`**
   - Main orchestration function for all system information spoofing
   - Coordinates CPU, RAM, GPU, and storage specification updates

### Enhanced CPU Spoofing

Updated `Modules\Hardware\AdvancedSpoofing\AdvancedSpoofing.psm1`:

#### Improvements:

1. **Dynamic Core Count Management**
   - Removes excess CPU cores beyond the spoofed profile
   - Creates CPU core registry entries to match spoofed processor
   - Ensures registry reflects exact core count from hardware profile

2. **Enhanced Environment Variables**
   - `NUMBER_OF_PROCESSORS`: Set to spoofed core count
   - `PROCESSOR_LEVEL`: Set to spoofed CPU family
   - `PROCESSOR_REVISION`: Set to spoofed CPU model/stepping
   - `PROCESSOR_IDENTIFIER`: Set to spoofed CPU identifier

3. **Improved WMI Modifications**
   - Includes `NumberOfCores` and `NumberOfLogicalProcessors`
   - Updates `ProcessorId` with spoofed CPU information
   - More comprehensive WMI data modification

### Registry Keys Modified

#### CPU Core Count:
- `HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Environment\NUMBER_OF_PROCESSORS`
- `HKLM:\HARDWARE\DESCRIPTION\System\NumberOfProcessors`
- `HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation\NumberOfProcessors`
- `HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation\NumberOfLogicalProcessors`
- `HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\[0-N]` (dynamic based on core count)

#### RAM Size:
- `HKLM:\HARDWARE\DESCRIPTION\System\TotalPhysicalMemory`
- `HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation\TotalPhysicalMemory`
- `HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation\InstalledMemory`
- `HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\TotalPhysicalMemory`

#### GPU Memory:
- `HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}\[subkeys]\HardwareInformation.MemorySize`
- `HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation\GraphicsMemory`
- `HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation\DedicatedVideoMemory`

#### Storage Capacity:
- `HKLM:\SYSTEM\CurrentControlSet\Enum\STORAGE\[subkeys]\Size`
- `HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation\TotalStorageCapacity`

## Configuration Updates

Updated `config.psd1` to support dynamic specifications:

```powershell
Memory = @{
    TotalSize = 34359738368  # 32GB in bytes (will be randomized)
    Speed = 3200
    Type = "DDR4"
    Manufacturer = "Samsung"
    UseRandomSize = $true  # Enable random RAM size generation (4GB-32GB)
}
```

## Integration with Main Hardware Spoofing

Updated `Modules\Hardware\HardwareSpoofing.psm1`:

1. **Import Statement**: Added import for SystemInformationSpoofing module
2. **Orchestration**: Added system information spoofing to main hardware spoofing workflow
3. **Execution Order**: System information spoofing runs after individual component spoofing

## Testing

### Test Script: `Test-DynamicHardwareSpecs.ps1`

Comprehensive test script to verify dynamic hardware specifications:

```powershell
# Analyze current hardware specifications
.\Test-DynamicHardwareSpecs.ps1 -Analyze

# Test dynamic hardware specifications spoofing
.\Test-DynamicHardwareSpecs.ps1 -TestDynamicSpecs

# Show verification information
.\Test-DynamicHardwareSpecs.ps1 -ShowSystemInfo
```

#### Test Features:
- **Before/After Comparison**: Shows hardware specs before and after spoofing
- **WMI Verification**: Checks WMI objects for hardware information
- **Registry Verification**: Checks registry keys for hardware specifications
- **Environment Variables**: Verifies environment variable updates
- **Comprehensive Coverage**: Tests CPU, RAM, GPU, and storage specifications

## Expected Results

After applying the enhancement:

### System Properties (This PC → Properties):
- **Processor**: Shows spoofed CPU name and specifications
- **Installed Memory (RAM)**: Shows randomized realistic RAM size (4GB-32GB)
- **System Type**: Shows correct architecture based on spoofed CPU

### Device Manager (devmgmt.msc):
- **Processors**: Shows correct number of CPU cores matching spoofed profile
- **Display Adapters**: Shows spoofed GPU with correct memory specifications

### Task Manager Performance Tab:
- **CPU**: Shows correct core count and CPU name
- **Memory**: Shows spoofed RAM size
- **GPU**: Shows spoofed GPU with correct VRAM

### System Information (msinfo32):
- **Processor**: Shows spoofed CPU specifications
- **Total Physical Memory**: Shows spoofed RAM size
- **Display**: Shows spoofed GPU information

## Random RAM Generation

The system now generates random but realistic RAM sizes:

- **Available Sizes**: 4GB, 8GB, 16GB, 32GB
- **Selection**: Random selection from common sizes
- **Consistency**: Same size used across all system information displays
- **Realistic Values**: Only uses standard RAM configurations

## Verification Steps

1. **Before Spoofing**:
   - Run `.\Test-DynamicHardwareSpecs.ps1 -Analyze`
   - Note current hardware specifications

2. **Apply Spoofing**:
   - Run `.\Test-DynamicHardwareSpecs.ps1 -TestDynamicSpecs`
   - Observe the selected hardware profile

3. **Verify Changes**:
   - Check System Properties (control system)
   - Check Device Manager (devmgmt.msc)
   - Check Task Manager Performance tab
   - Check System Information (msinfo32)

## Files Modified/Created

### New Files:
1. `Modules\Hardware\SystemInformation\SystemInformationSpoofing.psm1` - Main system information spoofing module
2. `Test-DynamicHardwareSpecs.ps1` - Comprehensive test script
3. `DYNAMIC_HARDWARE_SPECS_ENHANCEMENT.md` - This documentation

### Modified Files:
1. `Modules\Hardware\HardwareSpoofing.psm1` - Added system information spoofing integration
2. `Modules\Hardware\AdvancedSpoofing\AdvancedSpoofing.psm1` - Enhanced CPU spoofing with dynamic core count
3. `config.psd1` - Added random RAM size configuration option

## Benefits

1. **Realistic Hardware Display**: System Properties now shows specifications matching spoofed hardware
2. **Dynamic Core Count**: CPU core count properly reflects spoofed processor specifications
3. **Random RAM Values**: RAM size varies between realistic values (4GB-32GB)
4. **Comprehensive Coverage**: All major hardware specifications are dynamically updated
5. **Consistent Display**: Hardware information is consistent across all Windows interfaces
6. **Better VM Evasion**: More convincing hardware profile presentation

## Compatibility

- **Maintains Existing Functionality**: All existing hardware spoofing continues to work
- **Backward Compatible**: No breaking changes to existing configuration
- **Modular Design**: System information spoofing can be enabled/disabled independently
- **Integration**: Seamlessly integrates with existing hardware spoofing workflow

The dynamic hardware specifications enhancement provides a much more convincing and realistic hardware profile presentation, significantly improving the effectiveness of VM detection evasion.
