@{
    # =================================================================
    # Anti-VM Detection Toolkit - Modular Configuration
    # =================================================================
    # This configuration file controls all aspects of the modular
    # anti-VM detection system. Each module and sub-function can be
    # individually enabled or disabled for granular control.
    # =================================================================

    # Module Configuration (Simplified Structure)
    Modules = @{
        # Core Infrastructure Modules (Always Required)
        Core = @{
            Logging = @{ Enabled = $true }
            Configuration = @{ Enabled = $true }
            Validation = @{ Enabled = $true }
            Utilities = @{ Enabled = $true }
        }

        # Registry Operation Modules
        Registry = @{
            RegistryPrivileges = @{ Enabled = $true }
            RegistryModification = @{ Enabled = $true }
            RegistryCleanup = @{ Enabled = $true }
        }

        # Hardware Spoofing Modules
        Hardware = @{
            CPU = @{
                Enabled = $true
                spoofProcessorInfo = $true
                modifyFeatureSet = $true
                updateCacheInfo = $true
                generateRealisticSerial = $true
                modifyWMI = $true
                performSystemRefresh = $true
            }
            GPU = @{ Enabled = $true }
            Display = @{ Enabled = $true }
            Storage = @{ Enabled = $true }
            Memory = @{ Enabled = $true }
            Motherboard = @{ Enabled = $true }
            Network = @{ Enabled = $true }
            CDROM = @{ Enabled = $true }
            Audio = @{ Enabled = $true }
            USB = @{ Enabled = $true }
            Sensors = @{ Enabled = $true }
        }

        # System-Level Modules
        System = @{
            BIOS = @{ Enabled = $true }
            Drivers = @{ Enabled = $true }
            Services = @{ Enabled = $true }
            Processes = @{ Enabled = $true }
        }

        # FileSystem Operations
        FileSystem = @{
            FileCleanup = @{ Enabled = $true }
            DriverReplacement = @{ Enabled = $true }
        }

        # Behavioral Evasion
        Behavioral = @{
            Performance = @{ 
                Enabled = $false
                SimulateCPUUsage = $true
                SimulateMemoryUsage = $true
                SimulateDiskIO = $true
            }
            WMI = @{ Enabled = $true }
            UserSimulation = @{ 
                Enabled = $true
                CreateUserArtifacts = $true
                CreateFileArtifacts = $true
                SimulateBrowserUsage = $true
            }
        }

        # Advanced Detection Bypass
        Advanced = @{
            Hypervisor = @{ Enabled = $true }
            Memory = @{ Enabled = $true }
            Environment = @{ Enabled = $true }
            TimingAttacks = @{ 
                Enabled = $true
                IntroduceDelays = $true
            }
        }

    }

    # Hardware Specification Templates
    HardwareSpecs = @{
        CPU = @{
            vendor = "GenuineIntel"
            brand = "Intel(R) Core(TM) i7-12700K CPU @ 3.60GHz"
            manufacturer = "Intel Corporation"
            cores = 8
            threads = 16
            baseClock = 3600
            architecture = "x64"
            family = 6
            model = 151
            stepping = 2
            cacheL1 = 32768
            cacheL2 = 1310720
            cacheL3 = 26214400
        }
        GPU = @{
            Vendor = "NVIDIA Corporation"
            Model = "NVIDIA GeForce RTX 4070"
            ChipType = "NVIDIA GeForce RTX 4070"
            MemorySize = 12884901888
            VendorID = "10DE"
            DeviceID = "2783"
            DriverVersion = "31.0.15.4601"
            DriverDate = "7-19-2024"
        }
        Motherboard = @{
            Manufacturer = "ASUS"
            Model = "ROG STRIX Z690-E GAMING"
            Version = "Rev 1.xx"
            BIOSVendor = "American Megatrends Inc."
            BIOSVersion = "P2.90"
            BIOSDate = "03/15/2024"
        }
        Memory = @{
            TotalSize = 34359738368  # 32GB in bytes
            Speed = 3200
            Type = "DDR4"
            Manufacturer = "Samsung"
        }
        Storage = @{
            Model = "Samsung SSD 980 PRO 1TB"
            Size = 1099511627776  # 1TB in bytes
            SerialNumber = ""
        }
        Network = @{
            AdapterName = "Intel(R) Ethernet Controller I225-V"
            MACAddress = "00-1B-21-AA-BB-CC"
            Vendor = "Intel Corporation"
            VendorID = "8086"
            DeviceID = "15F3"
        }

        CDROM = @{
            Vendor = "HL-DT-ST"
            Model = "DVD-RAM GSA-H55N"
            Revision = "1.03"
        }

        Audio = @{
            Vendor = "Realtek"
            Model = "Realtek High Definition Audio"
            DeviceID = "HDAUDIO\\FUNC_01&VEN_10EC&DEV_0892"
            DriverVersion = "6.0.8967.1"
        }

        USB = @{
            Vendor = "Intel Corporation"
            Model = "Intel(R) USB 3.1 eXtensible Host Controller"
            DeviceID = "8086"
            DriverVersion = "10.0.22621.1"
        }
    }

    # Safety and Operational Settings
    safety = @{
        createBackups = $true
        performStabilityChecks = $true
        requireConfirmation = $false
        validateChanges = $true
        enableRollback = $true
        maxBackupAge = 30  # days
        backupCompression = $false
    }

    # Execution Control
    execution = @{
        parallelExecution = $false
        maxConcurrentModules = 3
        moduleTimeout = 300  # seconds
        retryFailedOperations = $true
        maxRetries = 3
        stopOnCriticalError = $true
    }

    # Output and Reporting
    output = @{
        verboseLogging = $false
        showProgress = $true
        generateReport = $true
        reportFormat = "JSON"  # JSON, XML, CSV
        includeTimings = $true
        showModifiedComponents = $true
    }

    # Advanced Features
    advanced = @{
        enableKernelModeOperations = $false
        useWMIHooks = $false
        enableMemoryPatching = $false
        performDeepRegistryScans = $true
        useAlternativeIdentifiers = $true
    }

    # Module Load Order (affects execution sequence)
    LoadOrder = @(
        "Core",
        "Registry",
        "Hardware", 
        "System",
        "FileSystem",
        "Behavioral",
        "Advanced"
    )

    # Version and Metadata
    version = "2.0-Modular"
    author = "Cybersecurity Research Team"
    lastModified = "2025-09-02"
    requiredPSVersion = "5.1"
    requiredOS = "Windows 10+"
    targetEnvironment = "VMware Workstation 16+/17+"
}
